import { defineConfig } from '@playwright/test'

export default defineConfig({
  use: {
    baseURL: 'http://localhost:9009', // Адрес Storybook
    headless: true, // Без UI
    viewport: { width: 1280, height: 720 },
  },
  // use: {
  //   baseURL: 'http://localhost:5173', // Указываем URL, если используем Vite (или другой dev-сервер)
  //   headless: true, // Можно поставить false, чтобы видеть браузер при тестировании
  //   viewport: { width: 1280, height: 720 },
  // },
  // webServer: {
  //   command: 'npm run dev',
  //   port: 5173, // Порт, на котором запускается ваше приложение
  //   reuseExistingServer: true,
  // },
  testDir: 'src', // 👈 Playwright будет искать тесты в папке src
  testMatch: ['**/*.spec.ts', '**/*.spec.tsx'], // 👈 Все файлы с .spec.ts
  snapshotDir: './__screenshots__', // Каталог для скриншотов
})
