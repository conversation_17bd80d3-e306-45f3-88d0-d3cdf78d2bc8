module.exports = {
  testMatch: ['**/*.test.{ts,tsx}'],
  testPathIgnorePatterns: [
    '<rootDir>/src/.*/spec\\.(ts|tsx)$', // playwright тесты
    '<rootDir>/dist/',
    '<rootDir>/node_modules/',
  ],
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.(ts|tsx|js|jsx|mjs)$': 'babel-jest',
    '^.+\\.css$': 'jest-css-modules-transform',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'], // Настройки для всех тестов
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1', // Поддержка алиасов
    '^.+\\.(css|scss|sass|less)$': 'identity-obj-proxy', // Для стилей
  },
  transformIgnorePatterns: ['node_modules/(?!uuid)/'],
  modulePathIgnorePatterns: ['<rootDir>/dist/', '<rootDir>/ckeditor4/', '<rootDir>/node_modules/'], // Исключение ненужных директорий
  collectCoverageFrom: ['src/**/*.{ts,tsx}', '!src/**/*.d.ts'], // Учет покрытия без `.d.ts`
  coverageReporters: ['text', 'lcov', 'html'], // Отчеты о покрытии в терминале и файле lcov
  coverageThreshold: {
    global: {
      statements: 80,
      branches: 75,
      functions: 80,
      lines: 80,
    },
  },
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage',
        filename: 'jest-report.html',
        expand: true,
        pageTitle: 'Jest Coverage Report',
      },
    ],
  ],
}
