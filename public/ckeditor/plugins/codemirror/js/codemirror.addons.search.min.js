!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/search/searchcursor",["../../lib/codemirror"],e):e(CodeMirror)}(function(r){"use strict";var m,y,v=r.Pos;function p(e,n){for(var t,o=null!=(o=(t=e).flags)?o:(t.ignoreCase?"i":"")+(t.global?"g":"")+(t.multiline?"m":""),r=o,i=0;i<n.length;i++)-1==r.indexOf(n.charAt(i))&&(r+=n.charAt(i));return o==r?e:new RegExp(e.source,r)}function g(e){return/\\s|\\n|\n|\\W|\\D|\[\^/.test(e.source)}function d(e,n,t){n=p(n,"g");for(var o=t.line,r=t.ch,i=e.lastLine();o<=i;o++,r=0){n.lastIndex=r;var a=e.getLine(o),a=n.exec(a);if(a)return{from:v(o,a.index),to:v(o,a.index+a[0].length),match:a}}}function i(e,n,t){if(!g(n))return d(e,n,t);n=p(n,"gm");for(var o=1,r=t.line,i=e.lastLine();r<=i;){for(var a=0;a<o&&!(i<r);a++)var s=e.getLine(r++),l=null==l?s:l+"\n"+s;o*=2,n.lastIndex=t.ch;var c,u,f,h=n.exec(l);if(h)return f=l.slice(0,h.index).split("\n"),c=h[0].split("\n"),u=t.line+f.length-1,f=f[f.length-1].length,{from:v(u,f),to:v(u+c.length-1,1==c.length?f+c[0].length:c[c.length-1].length),match:h}}}function x(e,n,t){for(var o,r=0;r<=e.length;){n.lastIndex=r;var i=n.exec(e);if(!i)break;var a=i.index+i[0].length;if(a>e.length-t)break;(!o||a>o.index+o[0].length)&&(o=i),r=i.index+1}return o}function C(e,n,t){n=p(n,"g");for(var o=t.line,r=t.ch,i=e.firstLine();i<=o;o--,r=-1){var a=e.getLine(o),a=x(a,n,r<0?0:a.length-r);if(a)return{from:v(o,a.index),to:v(o,a.index+a[0].length),match:a}}}function a(e,n,t){if(!g(n))return C(e,n,t);n=p(n,"gm");for(var o=1,r=e.getLine(t.line).length-t.ch,i=t.line,a=e.firstLine();a<=i;){for(var s=0;s<o&&a<=i;s++)var l=e.getLine(i--),c=null==c?l:l+"\n"+c;o*=2;var u,f,h,d=x(c,n,r);if(d)return h=c.slice(0,d.index).split("\n"),u=d[0].split("\n"),f=i+h.length,h=h[h.length-1].length,{from:v(f,h),to:v(f+u.length-1,1==u.length?h+u[0].length:u[u.length-1].length),match:d}}}function b(e,n,t,o){if(e.length==n.length)return t;for(var r=0,i=t+Math.max(0,e.length-n.length);;){if(r==i)return r;var a=r+i>>1,s=o(e.slice(0,a)).length;if(s==t)return a;t<s?i=a:r=1+a}}function s(e,n,t,o){if(!n.length)return null;var r=o?m:y,i=r(n).split(/\r|\n\r?/);e:for(var a=t.line,s=t.ch,l=e.lastLine()+1-i.length;a<=l;a++,s=0){var c=e.getLine(a).slice(s),u=r(c);if(1==i.length){var f=u.indexOf(i[0]);if(-1!=f)return t=b(c,u,f,r)+s,{from:v(a,b(c,u,f,r)+s),to:v(a,b(c,u,f+i[0].length,r)+s)}}else{f=u.length-i[0].length;if(u.slice(f)==i[0]){for(var h=1;h<i.length-1;h++)if(r(e.getLine(a+h))!=i[h])continue e;var d=e.getLine(a+i.length-1),p=r(d),g=i[i.length-1];if(p.slice(0,g.length)==g)return{from:v(a,b(c,u,f,r)+s),to:v(a+i.length-1,b(d,p,g.length,r))}}}}}function l(e,n,t,o){if(!n.length)return null;var r=o?m:y,i=r(n).split(/\r|\n\r?/);e:for(var a=t.line,s=t.ch,l=e.firstLine()-1+i.length;l<=a;a--,s=-1){var c=e.getLine(a),u=r(c=-1<s?c.slice(0,s):c);if(1==i.length){var f=u.lastIndexOf(i[0]);if(-1!=f)return{from:v(a,b(c,u,f,r)),to:v(a,b(c,u,f+i[0].length,r))}}else{f=i[i.length-1];if(u.slice(0,f.length)==f){for(var h=1,t=a-i.length+1;h<i.length-1;h++)if(r(e.getLine(t+h))!=i[h])continue e;var d=e.getLine(a+1-i.length),p=r(d);if(p.slice(p.length-i[0].length)==i[0])return{from:v(a+1-i.length,b(d,p,d.length-i[0].length,r)),to:v(a,b(c,u,f.length,r))}}}}}function o(t,o,e,n){var r;this.atOccurrence=!1,this.afterEmptyMatch=!1,this.doc=t,e=e?t.clipPos(e):v(0,0),this.pos={from:e,to:e},"object"==typeof n?r=n.caseFold:(r=n,n=null),"string"==typeof o?(null==r&&(r=!1),this.matches=function(e,n){return(e?l:s)(t,o,n,r)}):(o=p(o,"gm"),n&&!1===n.multiline?this.matches=function(e,n){return(e?C:d)(t,o,n)}:this.matches=function(e,n){return(e?a:i)(t,o,n)})}y=String.prototype.normalize?(m=function(e){return e.normalize("NFD").toLowerCase()},function(e){return e.normalize("NFD")}):(m=function(e){return e.toLowerCase()},function(e){return e}),o.prototype={findNext:function(){return this.find(!1)},findPrevious:function(){return this.find(!0)},find:function(e){var n=this.doc.clipPos(e?this.pos.from:this.pos.to);return this.afterEmptyMatch&&this.atOccurrence&&(n=v(n.line,n.ch),e?(n.ch--,n.ch<0&&(n.line--,n.ch=(this.doc.getLine(n.line)||"").length)):(n.ch++,n.ch>(this.doc.getLine(n.line)||"").length&&(n.ch=0,n.line++)),0!=r.cmpPos(n,this.doc.clipPos(n)))?this.atOccurrence=!1:(n=this.matches(e,n),this.afterEmptyMatch=n&&0==r.cmpPos(n.from,n.to),n?(this.pos=n,this.atOccurrence=!0,this.pos.match||!0):(n=v(e?this.doc.firstLine():this.doc.lastLine()+1,0),this.pos={from:n,to:n},this.atOccurrence=!1))},from:function(){if(this.atOccurrence)return this.pos.from},to:function(){if(this.atOccurrence)return this.pos.to},replace:function(e,n){this.atOccurrence&&(e=r.splitLines(e),this.doc.replaceRange(e,this.pos.from,this.pos.to,n),this.pos.to=v(this.pos.from.line+e.length-1,e[e.length-1].length+(1==e.length?this.pos.from.ch:0)))}},r.defineExtension("getSearchCursor",function(e,n,t){return new o(this.doc,e,n,t)}),r.defineDocExtension("getSearchCursor",function(e,n,t){return new o(this,e,n,t)}),r.defineExtension("selectMatches",function(e,n){for(var t=[],o=this.getSearchCursor(e,this.getCursor("from"),n);o.findNext()&&!(0<r.cmpPos(o.to(),this.getCursor("to")));)t.push({anchor:o.from(),head:o.to()});t.length&&this.setSelections(t,0)})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define("addon/dialog/dialog",["../../lib/codemirror"],e):e(CodeMirror)}(function(f){function h(e,n,t){var e=e.getWrapperElement(),o=e.appendChild(document.createElement("div"));return o.className=t?"CodeMirror-dialog CodeMirror-dialog-bottom":"CodeMirror-dialog CodeMirror-dialog-top","string"==typeof n?o.innerHTML=n:o.appendChild(n),f.addClass(e,"dialog-opened"),o}function d(e,n){e.state.currentNotificationClose&&e.state.currentNotificationClose(),e.state.currentNotificationClose=n}f.defineExtension("openDialog",function(e,n,t){t=t||{},d(this,null);var o=h(this,e,t.bottom),r=!1,i=this;function a(e){"string"==typeof e?s.value=e:r||(r=!0,f.rmClass(o.parentNode,"dialog-opened"),o.parentNode.removeChild(o),i.focus(),t.onClose&&t.onClose(o))}var s=o.getElementsByTagName("input")[0];return s?(s.focus(),t.value&&(s.value=t.value,!1!==t.selectValueOnOpen)&&s.select(),t.onInput&&f.on(s,"input",function(e){t.onInput(e,s.value,a)}),t.onKeyUp&&f.on(s,"keyup",function(e){t.onKeyUp(e,s.value,a)}),f.on(s,"keydown",function(e){t&&t.onKeyDown&&t.onKeyDown(e,s.value,a)||((27==e.keyCode||!1!==t.closeOnEnter&&13==e.keyCode)&&(s.blur(),f.e_stop(e),a()),13==e.keyCode&&n(s.value,e))}),!1!==t.closeOnBlur&&f.on(o,"focusout",function(e){null!==e.relatedTarget&&a()})):(e=o.getElementsByTagName("button")[0])&&(f.on(e,"click",function(){a(),i.focus()}),!1!==t.closeOnBlur&&f.on(e,"blur",a),e.focus()),a}),f.defineExtension("openConfirm",function(e,n,t){d(this,null);var o=h(this,e,t&&t.bottom),r=o.getElementsByTagName("button"),i=!1,a=this,s=1;function l(){i||(i=!0,f.rmClass(o.parentNode,"dialog-opened"),o.parentNode.removeChild(o),a.focus())}r[0].focus();for(var c=0;c<r.length;++c){var u=r[c];!function(n){f.on(u,"click",function(e){f.e_preventDefault(e),l(),n&&n(a)})}(n[c]),f.on(u,"blur",function(){--s,setTimeout(function(){s<=0&&l()},200)}),f.on(u,"focus",function(){++s})}}),f.defineExtension("openNotification",function(e,n){d(this,i);var t,o=h(this,e,n&&n.bottom),r=!1,e=n&&void 0!==n.duration?n.duration:5e3;function i(){r||(r=!0,clearTimeout(t),f.rmClass(o.parentNode,"dialog-opened"),o.parentNode.removeChild(o))}return f.on(o,"click",function(e){f.e_preventDefault(e),i()}),e&&(t=setTimeout(i,e)),i})}),function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror"),require("./searchcursor"),require("../dialog/dialog")):"function"==typeof define&&define.amd?define("addon/search/search.js",["../../lib/codemirror","./searchcursor","../dialog/dialog"],e):e(CodeMirror)}(function(f){"use strict";function n(){this.posFrom=this.posTo=this.lastQuery=this.query=null,this.overlay=null}function h(e){return e.state.search||(e.state.search=new n)}function r(e){return"string"==typeof e&&e==e.toLowerCase()}function d(e,n,t){return e.getSearchCursor(n,t,{caseFold:r(n),multiline:!0})}function p(e,n,t,o,r){e.openDialog?e.openDialog(n,r,{value:o,selectValueOnOpen:!0,bottom:e.options.search.bottom}):r(prompt(t,o))}function g(e){return e.replace(/\\([nrt\\])/g,function(e,n){return"n"==n?"\n":"r"==n?"\r":"t"==n?"\t":"\\"==n?"\\":e})}function i(e){var n=e.match(/^\/(.*)\/([a-z]*)$/);if(n)try{e=new RegExp(n[1],-1==n[2].indexOf("i")?"":"i")}catch(e){}else e=g(e);return e=("string"==typeof e?""==e:e.test(""))?/x^/:e}function m(e,n,t){var o;n.queryText=t,n.query=i(t),e.removeOverlay(n.overlay,r(n.query)),n.overlay=(o=n.query,t=r(n.query),"string"==typeof o?o=new RegExp(o.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),t?"gi":"g"):o.global||(o=new RegExp(o.source,o.ignoreCase?"gi":"g")),{token:function(e){o.lastIndex=e.pos;var n=o.exec(e.string);if(n&&n.index==e.pos)return e.pos+=n[0].length||1,"searching";n?e.pos=n.index:e.skipToEnd()}}),e.addOverlay(n.overlay),e.showMatchesOnScrollbar&&(n.annotate&&(n.annotate.clear(),n.annotate=null),n.annotate=e.showMatchesOnScrollbar(n.query,r(n.query)))}function t(r,n,e,t){var o=h(r);if(o.query)return y(r,n);var i,a,s,l,c,u=r.getSelection()||o.lastQuery;u instanceof RegExp&&"x^"==u.source&&(u=null),e&&r.openDialog?(i=null,a=function(e,n){f.e_stop(n),e&&(e!=o.queryText&&(m(r,o,e),o.posFrom=o.posTo=r.getCursor()),i&&(i.style.opacity=1),y(r,n.shiftKey,function(e,n){var t;n.line<3&&document.querySelector&&(t=r.display.wrapper.querySelector(".CodeMirror-dialog"))&&t.getBoundingClientRect().bottom-4>r.cursorCoords(n,"window").top&&((i=t).style.opacity=.4)}))},e=C(s=r),l=u,c=function(e,n){var t=f.keyName(e),o=r.getOption("extraKeys"),o=o&&o[t]||f.keyMap[r.getOption("keyMap")][t];"findNext"==o||"findPrev"==o||"findPersistentNext"==o||"findPersistentPrev"==o?(f.e_stop(e),m(r,h(r),n),r.execCommand(o)):"find"!=o&&"findPersistent"!=o||(f.e_stop(e),a(n,e))},s.openDialog(e,a,{value:l,selectValueOnOpen:!0,closeOnEnter:!1,onClose:function(){v(s)},onKeyDown:c,bottom:s.options.search.bottom}),t&&u&&(m(r,o,u),y(r,n))):p(r,C(r),"Search for:",u,function(e){e&&!o.query&&r.operation(function(){m(r,o,e),o.posFrom=o.posTo=r.getCursor(),y(r,n)})})}function y(t,o,r){t.operation(function(){var e=h(t),n=d(t,e.query,o?e.posFrom:e.posTo);(n.find(o)||(n=d(t,e.query,o?f.Pos(t.lastLine()):f.Pos(t.firstLine(),0))).find(o))&&(t.setSelection(n.from(),n.to()),t.scrollIntoView({from:n.from(),to:n.to()},20),e.posFrom=n.from(),e.posTo=n.to(),r)&&r(n.from(),n.to())})}function v(n){n.operation(function(){var e=h(n);e.lastQuery=e.query,e.query&&(e.query=e.queryText=null,n.removeOverlay(e.overlay),e.annotate)&&(e.annotate.clear(),e.annotate=null)})}function x(e,n){var t,o=e?document.createElement(e):document.createDocumentFragment();for(t in n)o[t]=n[t];for(var r=2;r<arguments.length;r++){var i=arguments[r];o.appendChild("string"==typeof i?document.createTextNode(i):i)}return o}function C(e){var n=x("label",{className:"CodeMirror-search-label"},e.phrase("Search:"),x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field",id:"CodeMirror-search-field"}));return n.setAttribute("for","CodeMirror-search-field"),x("",null,n," ",x("span",{style:"color: #666",className:"CodeMirror-search-hint"},e.phrase("(Use /re/ syntax for regexp search)")))}function b(n,o,r){n.operation(function(){for(var t,e=d(n,o);e.findNext();)"string"!=typeof o?(t=n.getRange(e.from(),e.to()).match(o),e.replace(r.replace(/\$(\d)/g,function(e,n){return t[n]}))):e.replace(r)})}function o(u,e){var n,t,o;u.getOption("readOnly")||(n=u.getSelection()||h(u).lastQuery,o=x("",null,x("span",{className:"CodeMirror-search-label"},t=e?u.phrase("Replace all:"):u.phrase("Replace:")),(o=u,x("",null," ",x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field"})," ",x("span",{style:"color: #666",className:"CodeMirror-search-hint"},o.phrase("(Use /re/ syntax for regexp search)"))))),p(u,o,t,n,function(c){c&&(c=i(c),p(u,x("",null,x("span",{className:"CodeMirror-search-label"},u.phrase("With:"))," ",x("input",{type:"text",style:"width: 10em",className:"CodeMirror-search-field"})),u.phrase("Replace with:"),"",function(i){var a,s,l;i=g(i),e?b(u,c,i):(v(u),a=d(u,c,u.getCursor("from")),l=function(t){a.replace("string"==typeof c?i:i.replace(/\$(\d)/g,function(e,n){return t[n]})),s()},(s=function(){var e,n,t,o,r=a.from();!(e=a.findNext())&&(a=d(u,c),!(e=a.findNext())||r&&a.from().line==r.line&&a.from().ch==r.ch)||(u.setSelection(a.from(),a.to()),u.scrollIntoView({from:a.from(),to:a.to()}),o=x("",null,x("span",{className:"CodeMirror-search-label"},(o=r=u).phrase("Replace?"))," ",x("button",{},o.phrase("Yes"))," ",x("button",{},o.phrase("No"))," ",x("button",{},o.phrase("All"))," ",x("button",{},o.phrase("Stop"))),n=u.phrase("Replace?"),t=[function(){l(e)},s,function(){b(u,c,i)}],r.openConfirm?r.openConfirm(o,t):confirm(n)&&t[0]())})())}))}))}f.defineOption("search",{bottom:!1}),f.commands.find=function(e){v(e),t(e)},f.commands.findPersistent=function(e){v(e),t(e,!1,!0)},f.commands.findPersistentNext=function(e){t(e,!1,!0,!0)},f.commands.findPersistentPrev=function(e){t(e,!0,!0,!0)},f.commands.findNext=t,f.commands.findPrev=function(e){t(e,!0)},f.commands.clearSearch=v,f.commands.replace=o,f.commands.replaceAll=function(e){o(e,!0)}}),function(e){"function"==typeof e.define&&e.define("addonSearch",["addon/search/search.js"],function(){})}(this);