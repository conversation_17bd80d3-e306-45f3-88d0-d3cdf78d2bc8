!function(e){"object"==typeof exports&&"object"==typeof module?e(require("../../lib/codemirror")):"function"==typeof define&&define.amd?define(["../../lib/codemirror","diff_match_patch"],e):e(CodeMirror)}(function(p){"use strict";var w=p.Pos,C="http://www.w3.org/2000/svg";function v(e,t){this.mv=e,this.type=t,this.classes="left"==t?{chunk:"CodeMirror-merge-l-chunk",start:"CodeMirror-merge-l-chunk-start",end:"CodeMirror-merge-l-chunk-end",insert:"CodeMirror-merge-l-inserted",del:"CodeMirror-merge-l-deleted",connect:"CodeMirror-merge-l-connect"}:{chunk:"CodeMirror-merge-r-chunk",start:"CodeMirror-merge-r-chunk-start",end:"CodeMirror-merge-r-chunk-end",insert:"CodeMirror-merge-r-inserted",del:"CodeMirror-merge-r-deleted",connect:"CodeMirror-merge-r-connect"}}function h(e){e.diffOutOfDate&&(e.diff=s(e.orig.getValue(),e.edit.getValue(),e.mv.options.ignoreWhitespace),e.chunks=c(e.diff),e.diffOutOfDate=!1,p.signal(e.edit,"updateDiff",e.diff))}var g=!(v.prototype={constructor:v,init:function(e,t,i){this.edit=this.mv.edit,(this.edit.state.diffViews||(this.edit.state.diffViews=[])).push(this),this.orig=p(e,I({value:t,readOnly:!this.mv.options.allowEditingOriginals},I(i))),"align"==this.mv.options.connect&&(this.edit.state.trackAlignable||(this.edit.state.trackAlignable=new V(this.edit)),this.orig.state.trackAlignable=new V(this.orig)),this.lockButton.title=this.edit.phrase("Toggle locked scrolling"),this.lockButton.setAttribute("aria-label",this.lockButton.title),this.orig.state.diffViews=[this];e=i.chunkClassLocation||"background";"[object Array]"!=Object.prototype.toString.call(e)&&(e=[e]),this.classes.classLocation=e,this.diff=s(r(t),r(i.value),this.mv.options.ignoreWhitespace),this.chunks=c(this.diff),this.diffOutOfDate=this.dealigned=!1,this.needsScrollSync=null,this.showDifferences=!1!==i.showDifferences},registerEvents:function(e){var t,i;this.forceUpdate=function(i){var t,r={from:0,to:0,marked:[]},n={from:0,to:0,marked:[]},o=!1;function l(e){o=!(g=!0),"full"==e&&(i.svg&&x(i.svg),i.copyButtons&&x(i.copyButtons),u(i.edit,r.marked,i.classes),u(i.orig,n.marked,i.classes),r.from=r.to=n.from=n.to=0),h(i),i.showDifferences&&(m(i.edit,i.diff,r,DIFF_INSERT,i.classes),m(i.orig,i.diff,n,DIFF_DELETE,i.classes)),"align"==i.mv.options.connect&&F(i),k(i),null!=i.needsScrollSync&&f(i,i.needsScrollSync),g=!1}function a(e){g||(i.dealigned=!0,s(e))}function s(e){g||o||(clearTimeout(t),!0===e&&(o=!0),t=setTimeout(l,!0===e?20:250))}function e(e,t){i.diffOutOfDate||(i.diffOutOfDate=!0,r.from=r.to=n.from=n.to=0),a(t.text.length-1!=t.to.line-t.from.line)}function c(){i.diffOutOfDate=!0,i.dealigned=!0,l("full")}i.edit.on("change",e),i.orig.on("change",e),i.edit.on("swapDoc",c),i.orig.on("swapDoc",c),"align"==i.mv.options.connect&&(p.on(i.edit.state.trackAlignable,"realign",a),p.on(i.orig.state.trackAlignable,"realign",a));return i.edit.on("viewportChange",function(){s(!1)}),i.orig.on("viewportChange",function(){s(!1)}),l(),l}(this),n(this,!0,!1),i=e,(t=this).edit.on("scroll",function(){f(t,!0)&&k(t)}),t.orig.on("scroll",function(){f(t,!1)&&k(t),i&&f(i,!0)&&k(i)})},setShowDifferences:function(e){(e=!1!==e)!=this.showDifferences&&(this.showDifferences=e,this.forceUpdate("full"))}});function f(e,t){var i,r,n,o,l,a,s,c,h;if(!e.diffOutOfDate)return e.needsScrollSync=null,!e.lockScroll||(i=+new Date,h=t?(a=e.edit,e.orig):(a=e.orig,e.edit),a.state.scrollSetBy==e&&(a.state.scrollSetAt||0)+250>i?void 0:(r=a.getScrollInfo(),"align"==e.mv.options.connect?l=r.top:(n=.5*r.clientHeight,c=r.top+n,o=a.lineAtHeight(c,"local"),o=function(e,t,i){for(var r,n,o,l,a=0;a<e.length;a++){var s=e[a],c=i?s.editFrom:s.origFrom,h=i?s.editTo:s.origTo;null==n&&(t<c?(n=s.editFrom,l=s.origFrom):t<h&&(n=s.editTo,l=s.origTo)),h<=t?(r=s.editTo,o=s.origTo):c<=t&&(r=s.editFrom,o=s.origFrom)}return{edit:{before:r,after:n},orig:{before:o,after:l}}}(e.chunks,o,t),a=d(a,t?o.edit:o.orig),o=d(h,t?o.orig:o.edit),c=(c-a.top)/(a.bot-a.top),(l=o.top-n+c*(o.bot-o.top))>r.top&&(s=r.top/n)<1?l=l*s+r.top*(1-s):(a=r.height-r.clientHeight-r.top)<n&&a<(c=h.getScrollInfo()).height-c.clientHeight-l&&(s=a/n)<1&&(l=l*s+(c.height-c.clientHeight-a)*(1-s))),h.scrollTo(r.left,l),h.state.scrollSetAt=i,h.state.scrollSetBy=e,1));e.lockScroll&&null==e.needsScrollSync&&(e.needsScrollSync=t)}function d(e,t){var i=t.after;return null==i&&(i=e.lastLine()+1),{top:e.heightAtLine(t.before||0,"local"),bot:e.heightAtLine(i,"local")}}function n(e,t,i){(e.lockScroll=t)&&0!=i&&f(e,DIFF_INSERT)&&k(e),(t?p.addClass:p.rmClass)(e.lockButton,"CodeMirror-merge-scrolllock-enabled")}function u(e,t,i){for(var r=0;r<t.length;++r){var n=t[r];if(n instanceof p.TextMarker)n.clear();else if(n.parent){c=s=a=l=o=void 0;for(var o=e,l=n,a=i,s=a.classLocation,c=0;c<s.length;c++)o.removeLineClass(l,s[c],a.chunk),o.removeLineClass(l,s[c],a.start),o.removeLineClass(l,s[c],a.end)}}t.length=0}function m(e,t,i,r,n){var o=e.getViewport();e.operation(function(){i.from==i.to||20<o.from-i.to||20<i.from-o.to?(u(e,i.marked,n),l(e,t,r,i.marked,o.from,o.to,n),i.from=o.from,i.to=o.to):(o.from<i.from&&(l(e,t,r,i.marked,o.from,i.from,n),i.from=o.from),o.to>i.to&&(l(e,t,r,i.marked,i.to,o.to,n),i.to=o.to))})}function y(e,t,i,r,n,o){for(var l=i.classLocation,a=e.getLineHandle(t),s=0;s<l.length;s++)r&&e.addLineClass(a,l[s],i.chunk),n&&e.addLineClass(a,l[s],i.start),o&&e.addLineClass(a,l[s],i.end);return a}function l(o,e,t,l,a,s,c){var i=w(0,0),r=w(a,0),n=o.clipPos(w(s-1)),h=t==DIFF_DELETE?c.del:c.insert;function g(e,t){for(var i=Math.max(a,e),r=Math.min(s,t),n=i;n<r;++n)l.push(y(o,n,c,!0,n==e,n==t-1));e==t&&i==t&&r==t&&(i?l.push(y(o,i-1,c,!1,!1,!0)):l.push(y(o,i,c,!1,!0,!1)))}for(var f,d=0,u=!1,m=0;m<e.length;++m){var p,v,k,b=e[m],C=b[0],b=b[1];C==DIFF_EQUAL?(v=i.line+(E(e,m)?0:1),N(i,b),v<(p=i.line+(D(e,m)?1:0))&&(u&&(g(d,v),u=!1),d=p)):(u=!0,C==t&&(v=N(i,b,!0),p=i,b=0<((C=r).line-p.line||C.ch-p.ch)?C:p,C=v,k=((k=n).line-C.line||k.ch-C.ch)<0?k:C,C=k,(f=b).line==C.line&&f.ch==C.ch||l.push(o.markText(b,k,{className:h})),i=v))}u&&g(d,i.line+1)}function k(e){if(e.showDifferences){e.svg&&(x(e.svg),t=e.gap.offsetWidth,_(e.svg,"width",t,"height",e.gap.offsetHeight)),e.copyButtons&&x(e.copyButtons);for(var t,i=e.edit.getViewport(),r=e.orig.getViewport(),n=e.mv.wrap.getBoundingClientRect().top,o=n-e.edit.getScrollerElement().getBoundingClientRect().top+e.edit.getScrollInfo().top,l=n-e.orig.getScrollerElement().getBoundingClientRect().top+e.orig.getScrollInfo().top,a=0;a<e.chunks.length;a++){var s=e.chunks[a];if(s.editFrom<=i.to&&s.editTo>=i.from&&s.origFrom<=r.to&&s.origTo>=r.from){g=c=f=h=b=k=v=p=m=u=d=void 0;var c,h,g,f,d=e,u=s,m=l,p=o,v=t,k="left"==d.type,b=d.orig.heightAtLine(u.origFrom,"local",!0)-m;d.svg&&(h=b,f=d.edit.heightAtLine(u.editFrom,"local",!0)-p,k&&(c=h,h=f,f=c),m=d.orig.heightAtLine(u.origTo,"local",!0)-m,g=d.edit.heightAtLine(u.editTo,"local",!0)-p,k&&(c=m,m=g,g=c),k=" C "+v/2+" "+f+" "+v/2+" "+h+" "+(v+2)+" "+h,c=" C "+v/2+" "+m+" "+v/2+" "+g+" -1 "+g,_(d.svg.appendChild(document.createElementNS(C,"path")),"d","M -1 "+f+k+" L "+(v+2)+" "+m+c+" z","class",d.classes.connect)),d.copyButtons&&(h=d.copyButtons.appendChild(B("div","left"==d.type?"⇝":"⇜","CodeMirror-merge-copy")),g=d.mv.options.allowEditingOriginals,h.title=d.edit.phrase(g?"Push to left":"Revert chunk"),h.chunk=u,h.style.top=(u.origTo>u.origFrom?b:d.edit.heightAtLine(u.editFrom,"local")-p)+"px",h.setAttribute("role","button"),h.setAttribute("tabindex","0"),h.setAttribute("aria-label",h.title),g)&&(f=d.edit.heightAtLine(u.editFrom,"local")-p,(k=d.copyButtons.appendChild(B("div","right"==d.type?"⇝":"⇜","CodeMirror-merge-copy-reverse"))).title="Push to right",k.chunk={editFrom:u.origFrom,editTo:u.origTo,origFrom:u.editFrom,origTo:u.editTo},k.style.top=f+"px","right"==d.type?k.style.left="2px":k.style.right="2px",k.setAttribute("role","button"),k.setAttribute("tabindex","0"),k.setAttribute("aria-label",k.title))}}}}function b(e,t){for(var i=0,r=0,n=0;n<t.length;n++){var o=t[n];if(o.editTo>e&&o.editFrom<=e)return null;if(o.editFrom>e)break;i=o.editTo,r=o.origTo}return r+(e-i)}function S(e,t,i){for(var r=e.state.trackAlignable,n=e.firstLine(),o=0,l=[],a=0;;a++){for(var s=t[a],c=s?i?s.origFrom:s.editFrom:1e9;o<r.alignable.length;o+=2){var h=r.alignable[o]+1;if(!(h<=n)){if(!(h<=c))break;l.push(h)}}if(!s)break;l.push(n=i?s.origTo:s.editTo)}return l}function T(e,t,i,r){var n=0,o=0,l=0,a=0;e:for(;;n++){var s=e[n],c=t[o];if(!s&&null==c)break;for(var h=s?s[0]:1e9,g=null==c?1e9:c;l<i.length;){var f=i[l];if(f.origFrom<=g&&f.origTo>g){o++,n--;continue e}if(f.editTo>h){if(f.editFrom<=h)continue e;break}a+=f.origTo-f.origFrom-(f.editTo-f.editFrom),l++}h==g-a?(s[r]=g,o++):h<g-a?s[r]=h+a:((c=[g-a,null,null])[r]=g,e.splice(n,0,c),o++)}}function F(e,t){if(e.dealigned||t){if(!e.orig.curOp)return e.orig.operation(function(){F(e,t)});e.dealigned=!1;for(var i=e.mv.left==e?e.mv.right:e.mv.left,r=(i&&(h(i),i.dealigned=!1),function(e,t){var i=S(e.edit,e.chunks,!1),r=[];if(t)for(var n=0,o=0;n<t.chunks.length;n++){for(var l=t.chunks[n].editTo;o<i.length&&i[o]<l;)o++;o!=i.length&&i[o]==l||i.splice(o++,0,l)}for(n=0;n<i.length;n++)r.push([i[n],null,null]);return T(r,S(e.orig,e.chunks,!0),e.chunks,1),t&&T(r,S(t.orig,t.chunks,!0),t.chunks,2),r}(e,i)),n=e.mv.aligners,o=0;o<n.length;o++)n[o].clear();n.length=0;var l=[e.edit,e.orig],a=[],s=[];i&&l.push(i.orig);for(o=0;o<l.length;o++)a.push(l[o].getScrollInfo().top),s.push(-l[o].getScrollerElement().getBoundingClientRect().top);(s[0]!=s[1]||3==l.length&&s[1]!=s[2])&&M(l,s,[0,0,0],n);for(var c=0;c<r.length;c++)M(l,s,r[c],n);for(o=0;o<l.length;o++)l[o].scrollTo(null,a[o])}}function M(e,t,i,r){for(var n,o=-1e8,l=[],a=0;a<e.length;a++)null!=i[a]&&(n=e[a].heightAtLine(i[a],"local")-t[a],l[a]=n,o=Math.max(o,n));for(var s,c,h,g,f,a=0;a<e.length;a++)null!=i[a]&&1<(h=o-l[a])&&r.push((s=e[a],c=i[a],h=h,g=!(f=g=void 0),c>s.lastLine()&&(c--,g=!1),(f=document.createElement("div")).className="CodeMirror-merge-spacer",f.style.height=h+"px",f.style.minWidth="1px",s.addLineWidget(c,f,{height:h,above:g,mergeSpacer:!0,handleMouseEvents:!0})))}function o(e,t,i,r){var n,o,l,a;e.diffOutOfDate||(n=r.origTo>i.lastLine()?w(r.origFrom-1):w(r.origFrom,0),o=w(r.origTo,0),l=r.editTo>t.lastLine()?w(r.editFrom-1):w(r.editFrom,0),r=w(r.editTo,0),(a=e.mv.options.revertChunk)?a(e.mv,i,n,o,t,l,r):t.replaceRange(i.getRange(n,o),l,r))}var a,L=p.MergeView=function(e,g){if(!(this instanceof L))return new L(e,g);function t(){h&&k(h),f&&k(f)}var i,r,n=(this.options=g).origLeft,o=null==g.origRight?g.orig:g.origRight,l=null!=n,a=null!=o,s=1+(l?1:0)+(a?1:0),c=[],h=this.left=null,f=this.right=null,d=this,l=(l&&(h=this.left=new v(this,"left"),i=B("div",null,"CodeMirror-merge-pane CodeMirror-merge-left"),c.push(i),c.push(A(h))),B("div",null,"CodeMirror-merge-pane CodeMirror-merge-editor")),u=(c.push(l),a&&(f=this.right=new v(this,"right"),c.push(A(f)),r=B("div",null,"CodeMirror-merge-pane CodeMirror-merge-right"),c.push(r)),(a?r:l).className+=" CodeMirror-merge-pane-rightmost",c.push(B("div",null,null,"height: 0; clear: both;")),this.wrap=e.appendChild(B("div",c,"CodeMirror-merge CodeMirror-merge-"+s+"pane"))),m=(this.edit=p(l,I(g)),h&&h.init(i,n,g),f&&f.init(r,o,g),g.collapseIdentical&&this.editor().operation(function(){var e=d,t=g.collapseIdentical;"number"!=typeof t&&(t=2);for(var i=[],r=e.editor(),n=r.firstLine(),o=n,l=r.lastLine();o<=l;o++)i.push(!0);e.left&&O(e.left,t,n,i),e.right&&O(e.right,t,n,i);for(var a=0;a<i.length;a++)if(i[a]){for(var s,c=a+n,h=1;a<i.length-1&&i[a+1];a++,h++);t<h&&(s=[{line:c,cm:r}],e.left&&s.push({line:b(c,e.left.chunks),cm:e.left.orig}),e.right&&s.push({line:b(c,e.right.chunks),cm:e.right.orig}),s=function(e,t){var i=[];function r(){for(var e=0;e<i.length;e++)i[e].clear()}for(var n=0;n<t.length;n++){var o=t[n],o=function(e,t,i){e.addLineClass(t,"wrap","CodeMirror-merge-collapsed-line");var r=document.createElement("span"),n=(r.className="CodeMirror-merge-collapsed-widget",r.title=e.phrase("Identical text collapsed. Click to expand."),e.markText(w(t,0),w(i-1),{inclusiveLeft:!0,inclusiveRight:!0,replacedWith:r,clearOnEnter:!0}));function o(){n.clear(),e.removeLineClass(t,"wrap","CodeMirror-merge-collapsed-line")}n.explicitlyCleared&&o();return p.on(r,"click",o),n.on("clear",o),p.on(r,"click",o),{mark:n,clear:o}}(o.cm,o.line,o.line+e);i.push(o),o.mark.on("clear",r)}return i[0].mark}(h,s),e.options.onCollapse)&&e.options.onCollapse(e,c,h,s)}}),"align"==g.connect&&(this.aligners=[],F(this.left||this.right,!0)),h&&h.registerEvents(f),f&&f.registerEvents(h),p.on(window,"resize",t),setInterval(function(){for(var e=u.parentNode;e&&e!=document.body;e=e.parentNode);e||(clearInterval(m),p.off(window,"resize",t))},5e3))};function A(t){var i,e=t.lockButton=B("div",null,"CodeMirror-merge-scrolllock"),r=(e.setAttribute("role","button"),e.setAttribute("tabindex","0"),B("div",[e],"CodeMirror-merge-scrolllock-wrap")),e=(p.on(e,"click",function(){n(t,!t.lockScroll)}),p.on(e,"keyup",function(e){"Enter"!==e.key&&"Space"!==e.code||n(t,!t.lockScroll)}),[r]);return!1!==t.mv.options.revertButtons&&(t.copyButtons=B("div",null,"CodeMirror-merge-copybuttons-"+t.type),p.on(t.copyButtons,"click",i=function(e){e=e.target||e.srcElement;e.chunk&&("CodeMirror-merge-copy-reverse"==e.className?o(t,t.orig,t.edit,e.chunk):o(t,t.edit,t.orig,e.chunk))}),p.on(t.copyButtons,"keyup",function(e){"Enter"!==e.key&&"Space"!==e.code||i(e)}),e.unshift(t.copyButtons)),"align"!=t.mv.options.connect&&((r=document.createElementNS&&document.createElementNS(C,"svg"))&&!r.createSVGRect&&(r=null),t.svg=r)&&e.push(r),t.gap=B("div",e,"CodeMirror-merge-gap")}function r(e){return"string"==typeof e?e:e.getValue()}function s(e,t,i){for(var r=(a=a||new diff_match_patch).diff_main(e,t),n=0;n<r.length;++n){var o=r[n];(i?/[^ \t]/.test(o[1]):o[1])?n&&r[n-1][0]==o[0]&&(r.splice(n--,1),r[n][1]+=o[1]):r.splice(n--,1)}return r}function c(e){var t=[];if(e.length){for(var i=0,r=0,n=w(0,0),o=w(0,0),l=0;l<e.length;++l){var a,s,c,h,g=e[l],f=g[0];f==DIFF_EQUAL?(s=!E(e,l)||n.line<i||o.line<r?1:0,a=n.line+s,s=o.line+s,N(n,g[1],null,o),h=D(e,l)?1:0,c=n.line+h,h=o.line+h,a<c&&(l&&t.push({origFrom:r,origTo:s,editFrom:i,editTo:a}),i=c,r=h)):N(f==DIFF_INSERT?n:o,g[1])}(i<=n.line||r<=o.line)&&t.push({origFrom:r,origTo:o.line+1,editFrom:i,editTo:n.line+1})}return t}function D(e,t){var i;return t==e.length-1||!(1==(i=e[t+1][1]).length&&t<e.length-2||10!=i.charCodeAt(0))&&(t==e.length-2||(1<(i=e[t+2][1]).length||t==e.length-3)&&10==i.charCodeAt(0))}function E(e,t){var i;return 0==t||10==(i=e[t-1][1]).charCodeAt(i.length-1)&&(1==t||10==(i=e[t-2][1]).charCodeAt(i.length-1))}function O(e,t,i,r){for(var n=0;n<e.chunks.length;n++)for(var o=e.chunks[n],l=o.editFrom-t;l<o.editTo+t;l++){var a=l+i;0<=a&&a<r.length&&(r[a]=!1)}}function B(e,t,i,r){var n=document.createElement(e);if(i&&(n.className=i),r&&(n.style.cssText=r),"string"==typeof t)n.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)n.appendChild(t[o]);return n}function x(e){for(var t=e.childNodes.length;0<t;--t)e.removeChild(e.firstChild)}function _(e){for(var t=1;t<arguments.length;t+=2)e.setAttribute(arguments[t],arguments[t+1])}function I(e,t){for(var i in t=t||{},e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function N(e,t,i,r){for(var n=i?w(e.line,e.ch):e,o=0;;){var l=t.indexOf("\n",o);if(-1==l)break;++n.line,r&&++r.line,o=l+1}return n.ch=(o?0:n.ch)+(t.length-o),r&&(r.ch=(o?0:r.ch)+(t.length-o)),n}L.prototype={constructor:L,editor:function(){return this.edit},rightOriginal:function(){return this.right&&this.right.orig},leftOriginal:function(){return this.left&&this.left.orig},setShowDifferences:function(e){this.right&&this.right.setShowDifferences(e),this.left&&this.left.setShowDifferences(e)},rightChunks:function(){if(this.right)return h(this.right),this.right.chunks},leftChunks:function(){if(this.left)return h(this.left),this.left.chunks}};var H=1,R=2,W=4;function V(e){this.cm=e,this.alignable=[],this.height=e.doc.height;var l=this;e.on("markerAdded",function(e,t){t.collapsed&&null!=(t=t.find(1))&&l.set(t.line,W)}),e.on("markerCleared",function(e,t,i,r){null!=r&&t.collapsed&&l.check(r,W,l.hasMarker)}),e.on("markerChanged",this.signal.bind(this)),e.on("lineWidgetAdded",function(e,t,i){t.mergeSpacer||(t.above?l.set(i-1,R):l.set(i,H))}),e.on("lineWidgetCleared",function(e,t,i){t.mergeSpacer||(t.above?l.check(i-1,R,l.hasWidgetBelow):l.check(i,H,l.hasWidget))}),e.on("lineWidgetChanged",this.signal.bind(this)),e.on("change",function(e,t){var i=t.from.line,r=t.to.line-t.from.line,n=t.text.length-1,o=i+n;(r||n)&&l.map(i,r,n),l.check(o,W,l.hasMarker),(r||n)&&l.check(t.from.line,W,l.hasMarker)}),e.on("viewportChange",function(){l.cm.doc.height!=l.height&&l.signal()})}function t(e,t){var i=null,r=e.state.diffViews,n=e.getCursor().line;if(r)for(var o=0;o<r.length;o++){var l=r[o],a=e==l.orig,l=(h(l),(t<0?function(e,t,i){for(var r=e.length-1;0<=r;r--){var n=e[r],n=(i?n.origTo:n.editTo)-1;if(n<t)return n}}:function(e,t,i){for(var r=0;r<e.length;r++){var n=e[r],n=i?n.origFrom:n.editFrom;if(t<n)return n}})(l.chunks,n,a));null==l||null!=i&&!(t<0?i<l:l<i)||(i=l)}if(null==i)return p.Pass;e.setCursor(i,0)}V.prototype={signal:function(){p.signal(this,"realign"),this.height=this.cm.doc.height},set:function(e,t){for(var i=-1;i<this.alignable.length;i+=2){var r=this.alignable[i]-e;if(0==r)return(this.alignable[i+1]&t)==t?void 0:(this.alignable[i+1]|=t,void this.signal());if(0<r)break}this.signal(),this.alignable.splice(i,0,e,t)},find:function(e){for(var t=0;t<this.alignable.length;t+=2)if(this.alignable[t]==e)return t;return-1},check:function(e,t,i){var r=this.find(e);-1!=r&&this.alignable[r+1]&t&&(i.call(this,e)||(this.signal(),(i=this.alignable[r+1]&~t)?this.alignable[r+1]=i:this.alignable.splice(r,2)))},hasMarker:function(e){var t=this.cm.getLineHandle(e);if(t.markedSpans)for(var i=0;i<t.markedSpans.length;i++)if(t.markedSpans[i].marker.collapsed&&null!=t.markedSpans[i].to)return!0;return!1},hasWidget:function(e){var t=this.cm.getLineHandle(e);if(t.widgets)for(var i=0;i<t.widgets.length;i++)if(!t.widgets[i].above&&!t.widgets[i].mergeSpacer)return!0;return!1},hasWidgetBelow:function(e){if(e!=this.cm.lastLine()){var t=this.cm.getLineHandle(e+1);if(t.widgets)for(var i=0;i<t.widgets.length;i++)if(t.widgets[i].above&&!t.widgets[i].mergeSpacer)return!0}return!1},map:function(e,t,i){for(var r=i-t,n=e+t,o=-1,l=-1,a=0;a<this.alignable.length;a+=2){var s=this.alignable[a];s==e&&this.alignable[a+1]&R&&(o=a),s==n&&this.alignable[a+1]&R&&(l=a),s<=e||(s<n?this.alignable.splice(a--,2):this.alignable[a]+=r)}-1<o&&((t=this.alignable[o+1])==R?this.alignable.splice(o,2):this.alignable[o+1]=t&~R),-1<l&&i&&this.set(e+i,R)}},p.commands.goNextDiff=function(e){return t(e,1)},p.commands.goPrevDiff=function(e){return t(e,-1)}});