export const icons = [
  'emailBold',
  'emailSmall',
  'emailOpenBold',
  'emailOpenMedium',
  'emailOpenSmall',
  'emailCloseBold',
  'emailCloseMedium',
  'emailCloseSmall',
  'emailPosted',
  'emailPosted2',
  'emailWarning',
  'settingsBold',
  'settingsSmall',
  'trashBold',
  'trashSmall',
  'editBold',
  'editSmall',
  'modulesCopy',
  'eye',
  'eyeDot',
  'closeBold',
  'closeSmall',
  'load',
  'clipBold',
  'clipBold2',
  'clipSmall',
  'file',
  'lightning',
  'editLanding',
  'lock',
  'lock2',
  'lock3',
  'web1Bold',
  'web2Bold',
  'web2Small',
  'arrowUndo',
  'arrowRedo',
  'refresh',
  'refreshDouble',
  'arrow',
  'arrowReplace',
  'arrowImport',
  'download',
  'chevroneBold',
  'chevroneMedium',
  'chevroneSmall',
  'key',
  'timer',
  'pauseCircle',
  'stopCircle',
  'stopCircleSmall',
  'playCircle',
  'sobaka',
  'copy',
  'plus',
  'format',
  'formatBold',
  'licenses',
  'save',
  'calendarBold',
  'calendarSmall',
  'peopleBold',
  'peopleSmall',
  'department',
  'hatGraduation',
  'search',
  'list',
  'fail',
  'success',
  'fire',
  'audio',
  'fish',
  'incident',
  'riskLevel',
  'riskLevelCircle',
  'question',
  'filter',
  'sort',
  'organizationBold',
  'organizationSmall',
  'modules',
  'otdel',
  'otdel2',
  'person',
  'course',
  'chapter',
  'topic',
  'sortArrowUp',
  'sortArrowDown',
  'sortArrowNo',
  'statistics',
  'minus',
  'box',
  'image',
  'chart',
  'emailing',
  'arrowRight',
  'chartWithArrow',
  'organizationCircles',
  'emailToSidebar',
  'educationMedium',
  'peoplesMedium',
  'arrowDown',
  'play',
  'fullscreenEnter',
  'fullscreenExit',
  'QRCode',
  'fullscreenExit',
  'dataEntered',
  'dragArrows',
  'dragDotted',
  'verticalDotted',
  'sortWithArrowDown',
  'sortWithArrowUp',
  'flagRussia',
  'flagUsa',
  'article',
  'quiz',
  'video',
  'import',
  'settings',
  'picture',
  'ellipsis',
  'replace',
  'trash',
  'headphones',
  'videoBig',
  'playerPause',
  'playerPlay',
  'playerSound',
  'openFullscreen',
  'closeFullscreen',
  'more',
  'graduation',
  'slide',
  'articleWarning',
  'repeat',
  'scorm',
  'dot',
  'stepPassed',
  'burger',
  'flagUz',
  'studentHat',
] as const
export type TIcons = (typeof icons)[number]

export declare namespace IconsProps {
  interface Own {
    className?: string
  }

  type Props = Own
}

export {}
