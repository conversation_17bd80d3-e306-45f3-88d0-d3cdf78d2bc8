import { FC } from 'react'
import { IconsProps } from '../icons.d'

const StudentHatIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='StudentHat'
      className={className}
      width='161'
      height='160'
      viewBox='0 0 161 160'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M119.667 115.219L119.645 115.242L119.234 115.647C118.866 115.998 118.311 116.498 117.561 117.099C116.058 118.301 113.765 119.912 110.618 121.525C104.314 124.759 94.592 128 81 128C67.8326 128 58.2979 124.958 51.9834 121.828L51.3818 121.525L50.8018 121.223C47.9523 119.712 45.8492 118.226 44.4404 117.1C43.8773 116.649 43.4239 116.254 43.0771 115.938L42.7656 115.647C42.5871 115.477 42.4532 115.343 42.3633 115.251C42.3523 115.24 42.3424 115.229 42.333 115.219V89.0918L61.168 99.3477C73.2475 105.926 88.7527 105.926 100.832 99.3477L119.667 89.0918V115.219ZM70.1387 33.6406C76.7287 30.1198 85.2715 30.1197 91.8613 33.6406L140.416 59.5811L92.0049 85.9434C85.3487 89.5676 76.6504 89.5678 69.9941 85.9434L28.0557 63.1064L22.1426 59.8867V93.5527C22.029 93.6121 21.839 93.6797 21.5713 93.6797C21.3035 93.6797 21.1135 93.6121 21 93.5527V62.2744L21.6055 60.8486L21.1631 59.8057L70.1387 33.6406Z'
        stroke='#C9CEDC'
        strokeWidth='10'
      />
    </svg>
  )
}

export default StudentHatIcon
