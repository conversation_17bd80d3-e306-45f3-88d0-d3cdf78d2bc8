import { FC } from 'react'
import styles from './button-card.module.scss'
import classNamesBind from 'classnames/bind'
import { ButtonCardProps } from './button-card.d'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { LockIcon } from '../Icon/icons/components'

const cx = classNamesBind.bind(styles)

export const ButtonCard: FC<ButtonCardProps.Props> = props => {
  const {
    children,
    className,
    fullWidth,
    loading,
    disabled,
    icon = 'lock',
    'data-testid': dataTestid = 'ButtonCard',
    ...otherProps
  } = props

  return (
    <button
      className={cx('button', className, {
        fullWidth,
        loading,
        disabled: disabled || loading,
      })}
      disabled={disabled || loading}
      data-testid={dataTestid}
      {...otherProps}
    >
      {children}
      {loading && <span data-testid={`${dataTestid}.Loader`} />}
      {icon && !loading && (
        <IconWrapper size='20' color='primary' data-testid={`${dataTestid}.Icon`}>
          <LockIcon />
        </IconWrapper>
      )}
    </button>
  )
}
