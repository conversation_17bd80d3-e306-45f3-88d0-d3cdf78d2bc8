import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import styles from './select-icon.module.scss'
import classNamesBind from 'classnames/bind'
import { IMenuItem, SelectIconProps } from './select-icon.d'
import { IconWrapper } from '../Icon/IconWrapper'
import { useEvent } from 'shared/hooks'
import { IIconDirection, IIconSize, TIconColor } from '../Icon'
import { EditBoldIcon } from '../Icon/icons/components'
import StudentHatIcon from '../Icon/icons/components/StudentHatIcon'

const cx = classNamesBind.bind(styles)

const renderIcon = (
  iconName: string,
  size: string,
  color: TIconColor,
  direction: string,
  className?: string,
  testId?: string,
) => {
  const iconWrapper = (IconComponent: FC<{ className?: string }>) => (
    <IconWrapper
      size={size as IIconSize}
      color={color}
      direction={direction as IIconDirection}
      className={className}
      data-testid={testId}
    >
      <IconComponent />
    </IconWrapper>
  )

  switch (iconName) {
    case 'studentHat':
      return iconWrapper(StudentHatIcon)
    default:
      return iconWrapper(EditBoldIcon)
  }
}

export const SelectIcon: FC<SelectIconProps.Props> = props => {
  const {
    className,
    classNameForIcon,
    icon = 'editBold',
    color = 'gray',
    hasBackground = true,
    disabled = false,
    onClick,
    size = '28',
    iconSize = '20',
    direction = 'up',
    rounded,
    hasShadow = false,
    dndListeners,
    dndAttributes,
    type,
    title,
    menu,
  } = props

  const iconColor: {
    [key: string]: TIconColor
  } = {
    gray70: 'gray70',
    gray: 'gray80',
    green: 'primary',
    primary: 'primary',
    red: 'red',
  }
  const [openMenu, setOpenMenu] = useState(false)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    if (!menu) return onClick && onClick(event)
    setOpenMenu(prev => !prev)
    event.stopPropagation()
  }

  const handleClickMenuItem = (
    event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    i: IMenuItem,
  ) => {
    i.onClick && i.onClick(i.name)
    setOpenMenu(() => false)
    event.stopPropagation()
  }

  const wrapper = useRef<HTMLDivElement>(null)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setOpenMenu(() => false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  useEffect(() => {
    console.log(openMenu, 'menu')
  }, [openMenu])

  return (
    <div className={cx('positionWrapper', `size--${size}`)} ref={wrapper}>
      <button
        type={type}
        className={cx(
          'wrapper',
          `color--${color}`,
          `size--${size}`,
          rounded && 'rounded',
          hasShadow && 'hasShadow',
          className,
          {
            disabled,
            hasBackground,
          },
        )}
        onClick={handleClick}
        disabled={disabled}
        {...dndListeners}
        {...dndAttributes}
        title={title}
      >
        {renderIcon(icon, iconSize, iconColor[color], direction, classNameForIcon)}
      </button>
      {menu && (
        <div className={cx('menu', { active: openMenu })}>
          {menu.map(i => {
            if (i.isVisible === false) return <React.Fragment key={`menu-item-${i.name}`} />

            return (
              <div
                key={`menu-item-${i.name}`}
                className={cx('item')}
                onClick={e => handleClickMenuItem(e, i)}
              >
                {i.text}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
