import { FC } from 'react'
import styles from './organization-children-list.module.scss'
import classNamesBind from 'classnames/bind'
import { OrganizationChildrenListProps } from './organization-children-list.d'
import { ButtonIcon, RoundedButton } from '@/shared/ui'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { CopyCoursesModal } from '@/shared/modals/copy-courses-modal'
import { useOrganizationChildrenList } from './use-organization-children-list'
import { useTranslation } from 'react-i18next'
import { SelectIcon } from '@/shared/ui/select-icon/select-icon'
import { SelectReadyThemesModal } from '@/pages/admin/create-course/compose/modals/select-ready-themes-modal/select-ready-themes-modal'

const cx = classNamesBind.bind(styles)

export const OrganizationChildrenList: FC<OrganizationChildrenListProps.Props> = props => {
  const { className, list, from } = props
  const { t } = useTranslation()

  const {
    onOrganizationClick,
    onStatisticsClick,
    onEditClick,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    isDeleteLoading,
    onDeleteClick,
    onDeleteClose,
    onCreateClick,
    openCopyCourses,
    openCopyThemes,
    selectedOrganizationId,
    onCopyCoursesClick,
    onCopyThemesClick,
    handleCopyCoursesSelect,
    handleCopyCoursesClose,
    handleCopyThemesSelect,
    handleCopyThemesClose,
  } = useOrganizationChildrenList({
    from,
  })

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title')}>
        {t('commons:child_orgs')}
        <RoundedButton size='24' onClick={onCreateClick} />
      </div>
      {!list.length && <div className={cx('notChildren')}>{t('commons:no_child_orgs')}</div>}
      {!!list.length && (
        <div className={cx('list', { disabled: isDeleteLoading })}>
          {list.map(item => {
            return (
              <div
                key={item.id}
                className={cx('listItem')}
                onClick={() => onOrganizationClick(item.id)}
              >
                <div>
                  <div className={cx('title')}>{item.title}</div>
                  <div className={cx('licenses')}>
                    {item.license.users_count} {t('commons:of')} {item.license.users_limit}{' '}
                    {t('commons:licenses')}
                  </div>
                </div>
                <div className={cx('icons')}>
                  <SelectIcon
                    icon='studentHat'
                    color='gray70'
                    size='28'
                    menu={[
                      {
                        name: 'courses',
                        text: t('commons:copy_courses'),
                        onClick: () => onCopyCoursesClick(item.id),
                      },
                      {
                        name: 'themes',
                        text: t('commons:copy_themes'),
                        onClick: () => onCopyThemesClick(item.id),
                      },
                    ]}
                  />
                  <ButtonIcon
                    icon='editBold'
                    color='gray70'
                    size='28'
                    onClick={e => onEditClick(e, item.id)}
                  />
                  <ButtonIcon
                    icon='statistics'
                    color='gray70'
                    size='28'
                    onClick={e => onStatisticsClick(e, item.id)}
                  />
                  <ButtonIcon
                    icon='trashBold'
                    color='gray70'
                    size='28'
                    onClick={e => onDeleteClick(e, item.id)}
                  />
                  <ButtonIcon icon='chevroneBold' color='gray70' size='28' />
                </div>
              </div>
            )
          })}
        </div>
      )}
      {deletedId && openDeleteModal && (
        <DeleteModal
          id={deletedId}
          onClose={onDeleteClose}
          title=''
          active={openDeleteModal}
          setActive={setOpenDeleteModal}
        />
      )}
      {selectedOrganizationId && (
        <CopyCoursesModal
          active={openCopyCourses}
          setActive={handleCopyCoursesClose}
          selected={[]}
          organizationId={selectedOrganizationId}
          handleSelect={handleCopyCoursesSelect}
        />
      )}
      {selectedOrganizationId && (
        <SelectReadyThemesModal
          open={openCopyThemes}
          onClose={handleCopyThemesClose}
          organizationId={selectedOrganizationId}
          onConfirm={handleCopyThemesSelect}
        />
      )}
    </div>
  )
}
