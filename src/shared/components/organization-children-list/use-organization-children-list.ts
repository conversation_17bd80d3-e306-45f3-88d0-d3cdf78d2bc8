/* eslint-disable no-empty-pattern */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { useState } from 'react'
import { organizationAPI } from '@/entities/organization'
import { createSearchParams, useNavigate } from 'react-router-dom'
import { coursesApi } from '@/entities/courses'

type TThemeCourse = {
  id: UUID
  title: string
  order: number
  index: number
  hasStep?: boolean
}

export const useOrganizationChildrenList = ({ from }: { from?: string }) => {
  const navigate = useNavigate()
  const onOrganizationClick = (id: UUID) => navigate(`/lk/admin/organization/${id}`)

  const [openCopyCourses, setOpenCopyCourses] = useState(false)
  const [openCopyThemes, setOpenCopyThemes] = useState(false)
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<UUID | null>(null)

  const onStatisticsClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/statistics`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const onEditClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/edit`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const [deletedId, setDeletedId] = useState<UUID | null>(null)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [deleteOrganization, { isLoading: isDeleteLoading }] =
    organizationAPI.useDeleteOrganizationMutation()
  const [copyCourses] = coursesApi.useCopyOrganizationCoursesMutation()
  const [copyThemes] = coursesApi.useCopyOrganizationThemesMutation()

  const onDeleteClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    setDeletedId(id)
    setOpenDeleteModal(true)
  }

  const onDeleteClose = async (id?: UUID | null) => {
    if (id) {
      await deleteOrganization(id).unwrap()
    }

    setDeletedId(null)
    setOpenDeleteModal(false)
  }

  const onCreateClick = () => {
    navigate('create')
  }

  const onCopyCoursesClick = (organizationId: UUID) => {
    setSelectedOrganizationId(organizationId)
    setOpenCopyCourses(true)
  }

  const onCopyThemesClick = (organizationId: UUID) => {
    setSelectedOrganizationId(organizationId)
    setOpenCopyThemes(true)
  }

  const handleCopyCoursesSelect = (organizationId: UUID, courses: UUID[]) => {
    copyCourses({ organizationId, coursesIds: courses }).unwrap()
    setOpenCopyCourses(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyCoursesClose = () => {
    setOpenCopyCourses(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyThemesSelect = (themes: TThemeCourse[], organizationId?: UUID) => {
    if (!organizationId) return
    const ids = themes.map(theme => theme.id)
    copyThemes({ organizationId, themesIds: ids }).unwrap()
    setOpenCopyThemes(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyThemesClose = () => {
    setOpenCopyThemes(false)
    setSelectedOrganizationId(null)
  }

  return {
    onOrganizationClick,
    onStatisticsClick,
    onEditClick,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    isDeleteLoading,
    onDeleteClick,
    onDeleteClose,
    onCreateClick,
    openCopyCourses,
    openCopyThemes,
    selectedOrganizationId,
    onCopyCoursesClick,
    onCopyThemesClick,
    handleCopyCoursesSelect,
    handleCopyCoursesClose,
    handleCopyThemesSelect,
    handleCopyThemesClose,
  }
}
