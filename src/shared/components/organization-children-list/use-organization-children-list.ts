/* eslint-disable no-empty-pattern */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { useState } from 'react'
import { organizationAPI } from 'entities/organization'
import { createSearchParams, useNavigate } from 'react-router-dom'
import { IMenuItem } from '@/shared/ui/select-icon'
import { useTranslation } from 'react-i18next'

export const useOrganizationChildrenList = ({ from }: { from?: string }) => {
  const navigate = useNavigate()
  const onOrganizationClick = (id: UUID) => navigate(`/lk/admin/organization/${id}`)
  const { t } = useTranslation()

  const [openCopyCourses, setOpenCopyCourses] = useState(false)
  const [openCopyThemes, setOpenCopyThemes] = useState(false)

  const onStatisticsClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/statistics`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const onEditClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/edit`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const [deletedId, setDeletedId] = useState<UUID | null>(null)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [deleteOrganization, { isLoading: isDeleteLoading }] =
    organizationAPI.useDeleteOrganizationMutation()

  const onDeleteClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    setDeletedId(id)
    setOpenDeleteModal(true)
  }

  const onDeleteClose = async (id?: UUID | null) => {
    if (id) {
      await deleteOrganization(id).unwrap()
    }

    setDeletedId(null)
    setOpenDeleteModal(false)
  }

  const onCreateClick = () => {
    navigate('create')
  }

  const menu: IMenuItem[] = [
    {
      name: 'courses',
      text: t('commons:add_employee'),
      onClick: () => setOpenCopyCourses(true),
    },
    {
      name: 'themes',
      text: t('commons:import_from_file'),
      onClick: () => setOpenCopyThemes(true),
    },
  ]

  return {
    onOrganizationClick,
    onStatisticsClick,
    onEditClick,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    isDeleteLoading,
    onDeleteClick,
    onDeleteClose,
    onCreateClick,
    menu,
    openCopyCourses,
    openCopyThemes,
  }
}
