.codex-editor__redactor {
  padding-top: 24px;
  padding-bottom: 24px !important;
  min-height: 735px;
  contain: layout;
  position: relative;
}

[id^='react-editor-js-'] {
  width: 100%;
  height: 100%;
  position: relative;
  contain: layout style;
}

.ce-toolbar .ce-popover--opened .ce-popover__container {
  max-height: 334px;
}

.ce-toolbar .ce-popover-item__icon {
  width: 36px;
  height: 36px;
  margin-right: 8px;
}

.ce-toolbar .ce-popover-item__icon svg {
  width: 36px;
  height: 36px;
}

.codex-editor__tool-icon path {
  stroke: revert-layer;
}

.ce-toolbar {
  z-index: 20;
}

.ce-toolbar .ce-popover-item {
  height: 52px;
  padding: 8px 12px;
}

.ce-popover__search {
  display: none;
}

.ce-toolbar .ce-popover-item__title {
  font: var(--font-text-1-normal);
}

.ce-block {
  margin-bottom: 16px;
}

.ce-block__content {
  max-width: 900px;
  padding: 0 16px;
}

.ce-toolbar__content {
  max-width: 900px;
}

.ce-inline-toolbar {
  transform: scale(1);
}

.ce-inline-toolbar .ce-popover-item[data-item-name='convert-to'] {
  display: none;
}

.ce-inline-toolbar .ce-popover-item-separator {
  display: none;
}

@media (max-width: 1024px) {
  .codex-editor__redactor {
    min-height: 70vh;
  }
}
