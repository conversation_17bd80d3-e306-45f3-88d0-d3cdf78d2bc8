import { useTranslation } from 'react-i18next'
import { IListItem, Select } from '../../ui'
import SortIcon from '../../ui/Icon/icons/components/SortIcon'
import { IconWrapper } from '../../ui/Icon/IconWrapper'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '../../../store'
import {
  DepartmentsTreeSliceState,
  departmentsTreeSlice,
  selectSearchVariant,
  selectSearchVariants,
} from './departments-tree-slice'

const cx = classNamesBind.bind(styles)

export const SearchVariantSelector = () => {
  const { t } = useTranslation('modals__organization-tree')
  const selectedSearchVariant = useAppSelector(selectSearchVariant)
  const dispatch = useAppDispatch()
  const defaultSelectItems: IListItem<DepartmentsTreeSliceState['searchVariant']>[] = [
    { id: 'departments', title: t('sort.by_departments') },
    { id: 'employees', title: t('sort.by_employees') },
  ]
  const searchVariants = useAppSelector(selectSearchVariants)
  const usageSearchVariants = defaultSelectItems.filter(v =>
    searchVariants.find(variant => variant === v.id),
  )

  return (
    <Select
      listClassName={cx('select-list')}
      wrapperClassName={cx('select')}
      textClassName={cx('select-text')}
      list={usageSearchVariants}
      handleChange={v => {
        dispatch(
          departmentsTreeSlice.actions.setSearchVariant(
            v?.id as DepartmentsTreeSliceState['searchVariant'],
          ),
        )
      }}
      renderLabel={item => (
        <div className={cx('label__custom')}>
          <IconWrapper direction='down'>
            <SortIcon />
          </IconWrapper>
          {item.title}
        </div>
      )}
      value={selectedSearchVariant}
      customValue={selectedSearchVariant}
      placeholder={t('commons:sorting_departments')}
    />
  )
}
