import { FC, useMemo, useState } from 'react'
import styles from './copy-courses-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { CopyCoursesModalProps } from './copy-courses-modal.d'
import { Modal } from 'shared/components'
import { Button, Checkbox, Loader } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { Course, coursesApi, CourseCard } from 'entities/courses'
import { useUserOrganizationId } from 'entities/employee'

const cx = classNamesBind.bind(styles)

export const CopyCoursesModal: FC<CopyCoursesModalProps.Props> = props => {
  const { className, active, setActive, selected = [], handleSelect, organizationId } = props
  const { t } = useTranslation('commons')
  const userOrganizationId = useUserOrganizationId()

  const {
    data: allCoursesData,
    error,
    isLoading,
  } = coursesApi.useGetCoursesByOrganizationIdQuery({
    organization_id: userOrganizationId ?? '',
    limit: Number.MAX_SAFE_INTEGER,
  })

  const coursesByType = useMemo(() => {
    const result: Record<'archived' | 'not_archived', Course[]> = {
      archived: [],
      not_archived: [],
    }
    if (!allCoursesData?.data?.length) {
      return result
    }

    for (const course of allCoursesData.data) {
      const pushedCourse = course as Course
      if (course.archived) {
        result.archived.push(pushedCourse)
      } else {
        result.not_archived.push(pushedCourse)
      }
    }

    return result
  }, [allCoursesData])

  const [courses, setCourses] = useState<UUID[]>(selected)

  const handleSelectCourse = (id: UUID) => {
    const position = courses.indexOf(id)

    if (position === -1) {
      setCourses(prev => [...prev, id])
    } else {
      setCourses(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
    }
  }

  const handleClick = () => {
    if (organizationId) {
      handleSelect(organizationId, courses)
    }
    setActive(() => false)
  }

  const handleClose = () => {
    setCourses(selected)
    setActive(() => false)
  }

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={handleClose}>
      <div className={cx('title')}>{t('copy_courses')}</div>
      <div className={cx('innerWrapper')}>
        {isLoading && <Loader size='56' loading />}
        {!isLoading && error && <div className='error-text'>{t('error_unexpected_with_dots')}</div>}
        {!isLoading && !error && allCoursesData?.data && !allCoursesData?.data.length && (
          <div className='emptyText'>{t('no_courses')}</div>
        )}
        {!isLoading &&
          !error &&
          coursesByType?.not_archived &&
          coursesByType?.not_archived?.length > 0 && (
            <div className={cx('inner')}>
              {coursesByType?.not_archived.map(c => {
                const isSelected = courses.indexOf(c.id) !== -1
                return (
                  <CourseCard
                    className={cx('card')}
                    key={c.id}
                    imageUrl={c.image_path}
                    title={c.title}
                    TopAdornment={
                      <Checkbox
                        type='square'
                        className={cx('checkbox')}
                        customChecked={isSelected}
                        onChange={() => handleSelectCourse(c.id)}
                      />
                    }
                  />
                )
              })}
            </div>
          )}
        {coursesByType?.archived?.length > 0 && (
          <>
            <h3>{t('archive_courses')}</h3>
            <div className={cx('inner')}>
              {coursesByType?.archived.map(c => {
                const isSelected = courses.indexOf(c.id) !== -1

                return (
                  <CourseCard
                    className={cx('card')}
                    key={c.id}
                    imageUrl={c.image_path}
                    title={c.title}
                    TopAdornment={
                      <Checkbox
                        type='square'
                        className={cx('checkbox')}
                        customChecked={isSelected}
                        onChange={() => handleSelectCourse(c.id)}
                      />
                    }
                  />
                )
              })}
            </div>
          </>
        )}
      </div>
      <div className={cx('footer')}>
        <Button color='gray' onClick={handleClose}>
          {t('cancel')}
        </Button>
        <Button color='green' onClick={handleClick} disabled={!courses.length}>
          {t('copy')}
        </Button>
      </div>
    </Modal>
  )
}
