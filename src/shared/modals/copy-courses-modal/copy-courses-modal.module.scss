.wrapper {
  .title {
    color: var(--color-gray-100);
    font: var(--font-text-1-semibold);
    margin-bottom: 24px;
  }

  .innerWrapper {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 24px;

    .inner {
      display: grid;
      gap: 16px;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));

      .card {
        .checkbox {
          position: absolute;
          right: 12px;
          top: 12px;
        }
      }
    }

    h3 {
      color: var(--color-gray-80);
      font: var(--font-text-2-medium);
      margin: 24px 0 16px 0;
    }

    .emptyText {
      color: var(--color-gray-60);
      font: var(--font-text-2-regular);
      text-align: center;
      padding: 40px 0;
    }
  }

  .footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}
