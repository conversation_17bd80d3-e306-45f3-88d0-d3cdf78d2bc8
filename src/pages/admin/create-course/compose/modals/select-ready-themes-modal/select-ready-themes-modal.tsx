import classNamesBind from 'classnames/bind'
import styles from './select-ready-themes-modal.module.scss'
import { Modal } from '@/shared/components'
import { Button, Checkbox, Loader, SearchInput } from '@/shared/ui'
import TopicIcon from '@/shared/ui/Icon/icons/components/TopicIcon'
import ArticleWarningIcon from '@/shared/ui/Icon/icons/components/ArticleWarningIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { useEffect, useMemo, useState } from 'react'
import { selectThemesByCourseId } from '@/store/slices/new-course'
import { themeApi } from '@/entities/themeCourse/model/api'
import { TTheme } from '@/entities/themeCourse/model/types'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useDebounce } from '@/shared/hooks'
import { useAppSelector } from '@/store'
import { ThemePreview } from '@/entities/themeCourse/ui/theme-preview'
import { useUserOrganizationId } from '@/entities/employee'

type TThemeCourse = {
  id: UUID
  title: string
  order: number
  index: number
  hasStep?: boolean
}

type Props = {
  open: boolean
  sectionId?: string
  onClose: () => void
  onConfirm: (selectedCourseThemes: TThemeCourse[], organizationId?: UUID) => void
  isLoading?: boolean
  organizationId?: UUID
}

const cx = classNamesBind.bind(styles)

const LIMIT = 20

export const SelectReadyThemesModal = ({
  open,
  sectionId,
  onClose,
  onConfirm,
  organizationId,
  isLoading = false,
}: Props) => {
  const { t } = useTranslation('pages__create-course')

  const [search, setSearch] = useState('')
  const debouncedSearch = useDebounce(search, 500)
  const [page, setPage] = useState(1)
  const [totalThemesCount, setTotalThemesCount] = useState(0)
  const [renderThemes, setRenderThemes] = useState<TTheme[] | null>(null)
  const [currentCourseTheme, setCurrentCourseTheme] = useState<TTheme | null>(null)
  const [selectedCourseThemes, setSelectedCourseThemes] = useState<TThemeCourse[]>([])
  const sectionThemeList = useAppSelector(state =>
    sectionId ? selectThemesByCourseId(state, sectionId) : [],
  )
  const [getThemeData] = themeApi.useLazyGetThemeByIdQuery()
  const userOrganizationId = useUserOrganizationId()

  const orgThemesArg = useMemo(() => {
    return {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      offset: (page - 1) * LIMIT,
      search: debouncedSearch,
    }
  }, [page, debouncedSearch, userOrganizationId])

  const {
    data: themesData,
    isLoading: isThemesDataLoading,
    isFetching: isThemesDataFetching,
  } = themeApi.useGetOrganizationThemesQuery(orgThemesArg)

  useEffect(() => {
    setPage(1)
    setRenderThemes([])
  }, [debouncedSearch])

  useEffect(() => {
    if (themesData?.data && !isThemesDataLoading) {
      setRenderThemes(prevThemes => {
        if (!prevThemes) return [...themesData.data]
        const uniqueIds: { [key: string]: boolean } = {}

        return [...prevThemes, ...themesData.data].filter(item => {
          if (uniqueIds[item.id]) {
            return false
          } else {
            uniqueIds[item.id] = true
            return true
          }
        })
      })

      setTotalThemesCount(themesData.total_count)
    }
  }, [themesData, isThemesDataLoading])

  const handleSelectThemeChange = (newTheme: TThemeCourse) => {
    setSelectedCourseThemes(prev => {
      if (prev.some(prevTheme => prevTheme.id === newTheme.id)) {
        return prev.filter(prevTheme => prevTheme.id !== newTheme.id)
      }

      return [...prev, newTheme]
    })
  }

  const isCanLoadMore = !!renderThemes && renderThemes.length < totalThemesCount

  const handleClickTheme = async (theme: TTheme) => {
    const data = await getThemeData(theme.id).unwrap()
    setCurrentCourseTheme(data)
  }

  const isHaveEmptyThemes = useMemo(
    () => selectedCourseThemes.some(selectedCourse => !selectedCourse.hasStep),
    [selectedCourseThemes],
  )

  return (
    <Modal setActive={onClose} active={open} className={cx('wrapper')}>
      <div className={cx('title')}>{t('select_ready_theme')}</div>
      <div className={cx('inner')}>
        <SearchInput
          placeholder={t('fast_search')}
          value={search}
          onChange={setSearch}
          className={cx('searchInput')}
          isLoading={isThemesDataFetching || isThemesDataLoading || isLoading}
        />
        <div className={cx('counter')}>
          {selectedCourseThemes.length > 0 &&
            t('themes_choosed', { count: selectedCourseThemes.length })}
        </div>
        <div id='theme-infinite-scroll' className={cx('list', 'scrollbar')}>
          {isThemesDataLoading && <Loader size='56' loading />}
          {renderThemes && renderThemes.length > 0 && (
            <InfiniteScroll
              className={cx('list')}
              dataLength={renderThemes.length}
              next={() => setPage(prevPage => prevPage + 1)}
              hasMore={isCanLoadMore}
              loader={<Loader size='56' loading />}
              scrollableTarget={'theme-infinite-scroll'}
            >
              {renderThemes.map((renderTheme, index) => {
                const isRenderThemeCurrentSelect = sectionThemeList?.some(
                  sectionTheme => sectionTheme.id === renderTheme.id,
                )

                return (
                  <div
                    key={renderTheme.id}
                    className={cx('listInner', 'name__wrapper', {
                      selected: currentCourseTheme && renderTheme.id === currentCourseTheme.id,
                    })}
                    onClick={() => handleClickTheme(renderTheme)}
                  >
                    <Checkbox
                      initialChecked={isRenderThemeCurrentSelect}
                      disabled={isRenderThemeCurrentSelect}
                      onChange={() =>
                        handleSelectThemeChange({
                          id: renderTheme.id,
                          title: renderTheme.title,
                          order: index + 1,
                          index,
                          hasStep: renderTheme.has_steps,
                        })
                      }
                    />
                    <IconWrapper size='20' color='gray80'>
                      <TopicIcon />
                    </IconWrapper>
                    <span>{renderTheme.title}</span>
                    {!renderTheme.has_steps && (
                      <IconWrapper className={cx('noThemesTooltip')} color='red'>
                        <ArticleWarningIcon />
                      </IconWrapper>
                    )}
                  </div>
                )
              })}
            </InfiniteScroll>
          )}
        </div>
        {currentCourseTheme && currentCourseTheme.steps.length ? (
          <div className={cx('preview', 'scrollbar')}>
            <ThemePreview themeData={currentCourseTheme} />
          </div>
        ) : (
          <div className={cx('placeholder')}>{t('no_steps_in_theme')}</div>
        )}
      </div>
      <div className={cx('buttonWrapper')}>
        <Button color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button
          loading={isLoading}
          disabled={isHaveEmptyThemes}
          onClick={() => {
            onConfirm(selectedCourseThemes, organizationId)
          }}
        >
          {t('commons:choose')}
        </Button>
      </div>
    </Modal>
  )
}
