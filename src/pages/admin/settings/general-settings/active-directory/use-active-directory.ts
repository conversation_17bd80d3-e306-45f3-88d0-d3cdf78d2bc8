import { useEffect, useState } from 'react'
import { settingsApi } from '@/entities/settings'
import { SubmitHandler, useForm } from 'react-hook-form'
import { prepareLDAPData } from './helpers'
import { ADAutoSyncSettings, IActiveDirectoryResponse } from '@/shared/types/store/settings'
import { useTranslation } from 'react-i18next'

const TRANSLATION_FILE = 'pages__settings__active_directory'

export const useActiveDirectory = () => {
  const { data, isError, isFetching } = settingsApi.useGetLDAPSettingsQuery()
  const [patchTrigger, { data: patchData, isLoading: isPatchLoading }] =
    settingsApi.useLDAPPatchMutation()
  const [isSSO, setIsSSO] = useState(() => !!data?.krb_file)

  const [open, setOpen] = useState(false)
  const [isAutoSyncEnabled, setIsAutoSyncEnabled] = useState(false)
  const [autoSyncTimer, setAutoSyncTimer] = useState<string>('24')

  const { data: autoSyncSettingsData } = settingsApi.useGetAutoSyncSettingsQuery()
  const [optionDelete, setOptionDelete] = useState(
    () => autoSyncSettingsData?.auto_sync_option_delete,
  )
  const [optionUpdate, setOptionUpdate] = useState(
    () => autoSyncSettingsData?.auto_sync_option_update,
  )
  const [patchAutoSync] = settingsApi.usePatchAutoSyncSettingsMutation()
  const form = useForm<IActiveDirectoryResponse>({
    values: { ...(patchData || data)!, password: '' },
  })

  const { t } = useTranslation(TRANSLATION_FILE)

  const onChangeIsSSO = () => {
    form.resetField('krb_file')

    setIsSSO(v => !v)
  }

  useEffect(() => {
    setIsSSO(!!data?.use_sso)
    if (data?.krb_file) form.setValue('krb_file', String(data?.krb_file))
  }, [data, form])

  const onSubmit: SubmitHandler<IActiveDirectoryResponse> = async submitData => {
    const preparedData = prepareLDAPData(submitData)
    preparedData?.append('use_sso', String(Boolean(isSSO)))
    if (!preparedData) return

    await patchTrigger(preparedData).unwrap()

    const newAdSyncData: ADAutoSyncSettings = {
      auto_sync_interval: Number(autoSyncTimer) ?? autoSyncSettingsData?.auto_sync_interval ?? 0,
      auto_sync_enabled: isAutoSyncEnabled ?? autoSyncSettingsData?.auto_sync_enabled ?? false,
      auto_sync_option_delete:
        optionDelete ?? autoSyncSettingsData?.auto_sync_option_delete ?? false,
      auto_sync_option_update:
        optionUpdate ?? autoSyncSettingsData?.auto_sync_option_update ?? false,
    }

    await patchAutoSync(newAdSyncData).unwrap()
  }

  const onReset = () => {
    if (!data?.krb_file && isSSO) {
      setIsSSO(false)
    }

    form.reset()
  }

  useEffect(() => {
    if (!autoSyncSettingsData) return
    setOptionDelete(autoSyncSettingsData?.auto_sync_option_delete)
    setOptionUpdate(autoSyncSettingsData?.auto_sync_option_update)
  }, [autoSyncSettingsData])

  useEffect(() => {
    if (!autoSyncSettingsData) return

    setIsAutoSyncEnabled(() => autoSyncSettingsData.auto_sync_enabled)
    setAutoSyncTimer(() => String(autoSyncSettingsData.auto_sync_interval))
  }, [autoSyncSettingsData])

  const isLoading = isPatchLoading || isFetching

  const isDisabled =
    isLoading ||
    !form.formState.isValid ||
    (isSSO && !form?.watch('krb_file')) ||
    !autoSyncTimer ||
    Number(autoSyncTimer) < 1

  const submitBtnText = isLoading ? t('commons:loading') : t('commons:save')

  return {
    isSSO,
    onChangeIsSSO,
    form,
    onSubmit,
    isDisabled,
    isError,
    onReset,
    isAutoSyncEnabled,
    setIsAutoSyncEnabled,
    autoSyncTimer,
    setAutoSyncTimer,
    optionDelete,
    setOptionDelete,
    optionUpdate,
    setOptionUpdate,
    isFetching,
    isPatchLoading,
    submitBtnText,
    isLoading,
    data,
    open,
    t,
    setOpen,
  }
}
